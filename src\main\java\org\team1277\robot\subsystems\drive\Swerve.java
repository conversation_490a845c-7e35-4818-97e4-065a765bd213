package org.team1277.robot.subsystems.drive;

import edu.wpi.first.math.Matrix;
import edu.wpi.first.math.geometry.Pose2d;
import edu.wpi.first.math.geometry.Rotation2d;
import edu.wpi.first.math.kinematics.ChassisSpeeds;
import edu.wpi.first.math.numbers.N1;
import edu.wpi.first.math.numbers.N3;
import edu.wpi.first.wpilibj2.command.Command;
import edu.wpi.first.wpilibj2.command.SubsystemBase;
import edu.wpi.first.wpilibj2.command.sysid.SysIdRoutine;
import org.team1277.lib.swerve.SwerveAPI;

/**
 * Swerve drive subsystem. This class provides the public interface for controlling the swerve
 * drivetrain and exposes commands for robot operation.
 */
public class Swerve extends SubsystemBase {
  private final SwerveAPI api;

  /** Creates a new Swerve subsystem with internal hardware configuration. */
  public Swerve() {
    api = new SwerveAPI();
  }

  @Override
  public void periodic() {
    // Refresh the swerve API to update all hardware inputs
    api.refresh();
  }

  /**
   * Runs the drive at the desired velocity.
   *
   * @param speeds Speeds in meters/sec
   */
  public void runVelocity(ChassisSpeeds speeds) {
    api.runVelocity(speeds);
  }

  /** Runs the drive in a straight line with the specified drive output. */
  public void runCharacterization(double output) {
    api.runCharacterization(output);
  }

  /** Stops the drive. */
  public void stop() {
    api.stop();
  }

  /**
   * Stops the drive and turns the modules to an X arrangement to resist movement. The modules will
   * return to their normal orientations the next time a nonzero velocity is requested.
   */
  public void stopWithX() {
    api.stopWithX();
  }

  /** Returns a command to run a quasistatic test in the specified direction. */
  public Command sysIdQuasistatic(SysIdRoutine.Direction direction) {
    return run(() -> runCharacterization(0.0))
        .withTimeout(1.0)
        .andThen(api.getSysIdRoutine().quasistatic(direction));
  }

  /** Returns a command to run a dynamic test in the specified direction. */
  public Command sysIdDynamic(SysIdRoutine.Direction direction) {
    return run(() -> runCharacterization(0.0))
        .withTimeout(1.0)
        .andThen(api.getSysIdRoutine().dynamic(direction));
  }

  /** Returns the position of each module in radians. */
  public double[] getWheelRadiusCharacterizationPositions() {
    return api.getWheelRadiusCharacterizationPositions();
  }

  /** Returns the average velocity of the modules in rad/sec. */
  public double getFFCharacterizationVelocity() {
    return api.getFFCharacterizationVelocity();
  }

  /** Returns the current odometry pose. */
  public Pose2d getPose() {
    return api.getPose();
  }

  /** Returns the current odometry rotation. */
  public Rotation2d getRotation() {
    return api.getRotation();
  }

  /** Resets the current odometry pose. */
  public void setPose(Pose2d pose) {
    api.setPose(pose);
  }

  /** Adds a new timestamped vision measurement. */
  public void addVisionMeasurement(
      Pose2d visionRobotPoseMeters,
      double timestampSeconds,
      Matrix<N3, N1> visionMeasurementStdDevs) {
    api.addVisionMeasurement(visionRobotPoseMeters, timestampSeconds, visionMeasurementStdDevs);
  }

  /** Returns the maximum linear speed in meters per sec. */
  public double getMaxLinearSpeedMetersPerSec() {
    return api.getMaxLinearSpeedMetersPerSec();
  }

  /** Returns the maximum angular speed in radians per sec. */
  public double getMaxAngularSpeedRadPerSec() {
    return api.getMaxAngularSpeedRadPerSec();
  }
}
