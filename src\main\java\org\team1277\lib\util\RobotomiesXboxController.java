package org.team1277.lib.util;

import edu.wpi.first.wpilibj.GenericHID;
import edu.wpi.first.wpilibj.Timer;
import edu.wpi.first.wpilibj2.command.Command;
import edu.wpi.first.wpilibj2.command.Commands;
import edu.wpi.first.wpilibj2.command.button.CommandXboxController;
import edu.wpi.first.wpilibj2.command.button.Trigger;
import java.util.function.BooleanSupplier;

public class RobotomiesXboxController extends CommandXboxController {
	Timer timer = new Timer();
	private GenericHID m_driveRmbl;

	/**
	 * construct an instance of an xbox controller.
	 *
	 * @param port
	 *            the port index on the DS that the controller is plugged into.
	 */
	public RobotomiesXboxController(int port) {
		super(port);
		m_driveRmbl = this.getHID();
	}

	/**
	 * return a Command that rumbled both sides of the driver controller at a
	 * specific intensity for a set amount of time. intensity should be between 0
	 * and 1
	 */
	public Command rumbleForTime(double seconds, double intensity) {
		return rumbleForTime(m_driveRmbl, seconds, intensity);
	}

	/**
	 * return a Command that rumbled both sides of the specified controller at a
	 * specific intensity for a set amount of time. intensity should be between 0
	 * and 1
	 */
	public Command rumbleForTime(GenericHID controller, double seconds, double intensity) {
		return Commands.startEnd(() -> {
			timer.restart();
			controller.setRumble(GenericHID.RumbleType.kBothRumble, intensity);
		}, () -> {
			controller.setRumble(GenericHID.RumbleType.kBothRumble, 0);
		}).until(() -> timer.get() >= seconds);
	}

	/**
	 * return a Command that rumbled both sides of the driver controller at a
	 * specific intensity until a condition is met. intensity should be between 0
	 * and 1
	 */
	public Command rumbleUntilCondition(double intensity, BooleanSupplier condition) {
		return Commands.startEnd(() -> {
			m_driveRmbl.setRumble(GenericHID.RumbleType.kBothRumble, intensity);
		}, () -> {
			m_driveRmbl.setRumble(GenericHID.RumbleType.kBothRumble, 0);
		}).until(condition);
	}

	/** alias for (same as) {@link #back()} */
	public Trigger leftPaddle() {
		return back();
	}

	/** alias for (same as) {@link #start()} */
	public Trigger rightPaddle() {
		return start();
	}

	/**
	 * make joystick inputs more responsive with fine movement <br>
	 * </br>
	 * <strong>EXPERIMENTAL</strong>
	 *
	 * @param input
	 *            joystick axis input
	 * @param factor
	 *            exponential scaling factor
	 * @return smoothed input
	 */
	public double processInput(double input, double factor) {
		if (factor <= 0.0)
			return 0;

		if (Math.abs(input) < 0.05)
			return 0;

		int sign = input > 0 ? 1 : -1;

		return sign * Math.pow(Math.abs(input), 1 + Math.abs(input) * factor);
	}

	/**
	 * make joystick inputs more responsive with fine movement <br>
	 * </br>
	 * <strong>EXPERIMENTAL</strong>
	 *
	 * @param input
	 *            joystick axis input
	 * @return smoothed input
	 */
	public double processInput(double input) {
		return processInput(input, 0.2);
	}
}
