package org.team1277.robot;

import edu.wpi.first.math.geometry.Pose2d;
import edu.wpi.first.math.geometry.Rotation2d;
import edu.wpi.first.wpilibj2.command.Commands;
import org.team1277.lib.util.RobotomiesXboxController;
import org.team1277.robot.commands.DriveCommands;
import org.team1277.robot.subsystems.drive.Swerve;

public class RobotContainer {
  private final Swerve swerve;

  private final RobotomiesXboxController controller = new RobotomiesXboxController(0);

  public RobotContainer() {
    swerve = new Swerve();
    configureButtonBindings();
  }

  private void configureButtonBindings() {
    // Normal field-relative drive
    swerve.setDefaultCommand(
        DriveCommands.joystickDrive(
            swerve,
            () -> -controller.getLeftY(),
            () -> -controller.getLeftX(),
            () -> -controller.getRightX()));

    // Lock heading to 0°
    controller
        .a()
        .whileTrue(
            DriveCommands.joystickDriveAtAngle(
                swerve,
                () -> -controller.getLeftY(),
                () -> -controller.getLeftX(),
                Rotation2d::new));

    // Point modules into an X pattern/formation
    controller.x().onTrue(Commands.runOnce(swerve::stopWithX, swerve));

    // Reset gyro to 0°
    controller
        .b()
        .onTrue(
            Commands.runOnce(
                    () ->
                        swerve.setPose(
                            new Pose2d(swerve.getPose().getTranslation(), new Rotation2d())),
                    swerve)
                .ignoringDisable(true));
  }
}
